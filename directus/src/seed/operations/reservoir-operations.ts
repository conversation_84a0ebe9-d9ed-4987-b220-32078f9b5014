/**
 * Database operations for reservoir-related entities
 */

import type { Knex } from "knex";
import type { ReservoirData } from "../types";

/**
 * Create a reservoir in the database
 */
export async function createReservoir(
  trx: Knex,
  data: ReservoirData
): Promise<string> {
  const [reservoir] = await trx("reservoir")
    .insert({
      property: data.property,
      name: data.name,
      reservoir_monitor: data.reservoir_monitor,
      water_pump: data.water_pump,
      description: data.description,
      capacity: data.capacity,
      enabled: data.enabled,
      notes: data.notes,
    })
    .returning("id");
  return reservoir.id || reservoir;
}
