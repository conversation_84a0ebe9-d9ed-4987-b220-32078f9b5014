/**
 * Database operations for project-related entities
 */

import type { Knex } from "knex";
import type { ProjectData } from "../types";

/**
 * Create a project in the database
 */
export async function createProject(trx: Knex, data: ProjectData): Promise<string> {
  const [project] = await trx("project")
    .insert({
      property: data.property,
      name: data.name,
      description: data.description,
      irrigation_water_pump: data.irrigation_water_pump,
      fertigation_water_pump: data.fertigation_water_pump,
      localized_irrigation_controller: data.localized_irrigation_controller,
      pipe_wash_time_seconds: data.pipe_wash_time_seconds,
      backwash_duration_seconds: data.backwash_duration_seconds,
      backwash_period_seconds: data.backwash_period_seconds,
      start_date: data.start_date || "2024-01-01",
      end_date: data.end_date || "2024-12-31",
    })
    .returning("id");
  return project.id || project;
}
