/**
 * Business logic for creating sectors and related entities
 */

import type { Knex } from "knex";
import type { SectorCreationData } from "../types";
import { createDevice, associateDeviceWithProperty, createMeshDeviceMapping } from "../operations";

/**
 * Create sectors for a project including valve controllers and mesh mappings
 */
export async function createSectorsForProject(
  trx: Knex,
  data: SectorCreationData & { licPropertyDeviceId: string }
): Promise<string[]> {
  const vdDevices = [];
  const vdPropertyDeviceIds = [];

  // Create VD devices and their property device associations
  for (const vdDeviceData of data.vdDevicesData) {
    const vdDeviceId = await createDevice(trx, vdDeviceData);
    vdDevices.push(vdDeviceId);

    const vdPdId = await associateDeviceWithProperty(trx, {
      device: vdDeviceId,
      property: data.property,
    });
    vdPropertyDeviceIds.push(vdPdId);
  }

  // Create mesh device mappings for valve controllers BEFORE creating sectors
  const referenceStart = new Date();
  referenceStart.setDate(referenceStart.getDate() - 10); // active since 10 days ago

  const historyStart = new Date();
  historyStart.setDate(historyStart.getDate() - 30);
  const historyEnd = new Date();
  historyEnd.setDate(historyEnd.getDate() - 20);

  for (const vdPdId of vdPropertyDeviceIds) {
    // Historical mapping (ended before current period)
    await createMeshDeviceMapping(trx, {
      mesh_property_device: vdPdId,
      lic_property_device: data.licPropertyDeviceId,
      start_date: historyStart,
      end_date: historyEnd,
    });

    // Active mapping (no end_date -> indefinite)
    await createMeshDeviceMapping(trx, {
      mesh_property_device: vdPdId,
      lic_property_device: data.licPropertyDeviceId,
      start_date: referenceStart,
      end_date: null,
    });

    // Update current mesh mapping via DB function
    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
      [vdPdId]
    );
  }

  // Create sectors
  const sectors = [];
  for (const sectorData of data.sectorsData) {
    const [sector] = await trx("sector")
      .insert({
        project: data.project,
        name: sectorData.name,
        description: sectorData.description,
        valve_controller: vdDevices[sectorData.valveControllerIndex],
        valve_controller_output: sectorData.valve_controller_output,
        area: sectorData.area,
      })
      .returning("id");
    sectors.push(sector.id || sector);
  }

  return sectors;
}
