# Task list info:

- name: 250814_01
- base_branch: develop

---

# Tasks

## Task 1 - Refactor HardwarePage into isolated components

**Description**: Break down the monolithic HardwarePage.tsx into smaller, more maintainable components following single responsibility principle.

**Current Issues**:

- HardwarePage handles both devices and pumps logic
- Over 450 lines in a single component
- Multiple modals and state management in one place
- Difficult to test and maintain

**Proposed Structure**:

### Main Components

- `HardwarePage.tsx` (simplified orchestrator)
- `DevicesTab.tsx` (device-specific logic)
- `PumpsTab.tsx` (pump-specific logic)
- `HardwareSearchBar.tsx` (shared search component)
- `HardwareTabs.tsx` (tab navigation component)

### Device Components

- `DeviceList.tsx` (device list rendering)
- `EmptyDeviceState.tsx` (empty state component)

### Pump Components

- `PumpList.tsx` (pump list rendering)
- `PumpListItem.tsx` (individual pump item)
- `EmptyPumpState.tsx` (empty state component)

### Custom Hooks

- `useDeviceFiltering.tsx` (device filtering logic)
- `useDeviceSearch.tsx` (device search logic)
- `useDeviceActions.tsx` (device CRUD operations)
- `usePumpSearch.tsx` (pump search logic)
- `usePumpActions.tsx` (pump CRUD operations)
- `useHardwareModals.tsx` (modal state management)
- `useMeshDeviceMapping.tsx` (mesh network operations)
- `useTabNavigation.tsx` (tab state management)

**Benefits**:

- Single responsibility principle
- Better testability
- Improved maintainability
- Component reusability
- Performance optimizations through memoization

**Files to modify**:

- `app/src/pages/main/HardwarePage.tsx`
- Create new component files in appropriate directories
- Update imports and exports

**Acceptance Criteria**:

- [ ] Each component has a single, clear responsibility
- [ ] Custom hooks extract shared logic appropriately
- [ ] All existing functionality preserved
- [ ] Components follow project conventions (Jotai, TypeScript, TailwindCSS)
- [ ] No performance regressions
- [ ] Code passes type checking

**Target directories**

- app/src/pages/main (main HardwarePage component)
- app/src/pages/main/components (new tab components)
- app/src/hooks (custom hooks for extracted logic)

**Status:** Done

## Task 2 - PumpListItem references display data

**Description**:
PumpListItem should display the label and identifier of the pump controller, if available. Right now it is showing the id, which is not very user-friendly as it is a uuid. It should also display the LIC label and identifier which the pump controller is mapped to, if available.
Note: the label is stored in the property_device metadata, not in the device itself.

**Files to modify**:

- `app/src/pages/main/components/PumpListItem.tsx`
- `app/src/pages/main/components/PumpList.tsx` (if necessary)

**Acceptance Criteria**:

- [x] PumpListItem displays pump controller label and identifier
- [x] PumpListItem displays LIC label and identifier if available
- [x] All existing functionality preserved
- [x] Code passes type checking

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 2.1 - Create data mapping helper utility

**Description**
Create a utility function to resolve device UUIDs to user-friendly labels from property_device metadata. This helper will find property_device by device UUID, extract label and identifier from metadata, and handle null/undefined cases gracefully.

**Target directories**

- app/src/utils (utility functions)

**Status:** Done

### Subtask 2.2 - Update PumpListItem to use device labels

**Description**
Modify PumpListItem component to display pump controller label instead of UUID. Integrate the data mapping helper, update JSX to show controller label + identifier, and maintain existing fallback behavior.

**Target directories**

- app/src/pages/main/components (PumpListItem component)

**Status:** Done

### Subtask 2.3 - Add LIC information display

**Description**
Extend PumpListItem to show associated LIC information when available. Resolve mesh device mapping to find LIC, display LIC label and identifier, and handle cases where no LIC mapping exists.

**Target directories**

- app/src/pages/main/components (PumpListItem component)

**Status:** Done

## Task 3 - EnhancedDeviceListItem references display data

**Description**:
EnhancedDeviceListItem should display data from the device associated elements, like pump, reservoir, project, etc, if available.
For example, if a LIC is associated with a project, it should display the project name. if a RM is associated with a reservoir, it should display the reservoir name.
Find all possible element associations for each device type and display them if available.

**Files to modify**:

- `app/src/pages/main/components/EnhancedDeviceListItem.tsx`
- `app/src/pages/main/components/DeviceList.tsx` (if necessary)

**Acceptance Criteria**:

- [x] EnhancedDeviceListItem displays all available associated element data
- [x] All existing functionality preserved
- [x] Code passes type checking

**Target directories**

- app (frontend)

**Status:** Done

# Task 4 - More PumpListItem and EnhancedDeviceListItem references display data

**Description**:
For PumpListItem, find out entities that can have associations with water pumps, like project and reservoir. Display them if available. Also, make sure to display the current pump controller's mapped LIC information (label and identifier) through the mesh device mapping, if available.

For EnhancedDeviceListItem, if the device is a valve controller associated to sectors, also display the sector's project. Example: Setor 1, Setor 2 e Setor 3 em Projeto XYZ.
Keep in mind that the sector associated to the device can belong to different projects, so all projects must be displayed. Example: Setor 1 e Setor 2 em Projeto XYZ e Setor 3 em Projeto ABC.

**Files to modify**:

- `app/src/pages/main/components/PumpListItem.tsx`
- `app/src/pages/main/components/EnhancedDeviceListItem.tsx`
- `app/src/pages/main/components/DeviceList.tsx` (if necessary)

**Acceptance Criteria**:

- [x] PumpListItem displays all available associated element data
- [x] EnhancedDeviceListItem displays all available associated element data
- [x] All existing functionality preserved
- [x] Code passes type checking

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.1 - Enhance PumpListItem with project and reservoir associations

**Description**
Add project and reservoir association display to PumpListItem. Water pumps can be associated with projects through `irrigation_projects` and `fertigation_projects` fields, and with reservoirs through the reservoir's `water_pump` field.

**Target directories**

- app/src/pages/main/components (PumpListItem component)
- app/src/utils (device association utilities)

**Status:** Done

### Subtask 4.2 - Enhance EnhancedDeviceListItem sector-project grouping

**Description**
For valve controllers (VC), improve the sector association display to group sectors by their respective projects with proper Portuguese formatting (e.g., "Setor 1, Setor 2 e Setor 3 em Projeto XYZ").

**Target directories**

- app/src/pages/main/components (EnhancedDeviceListItem component)
- app/src/utils (device association utilities)

**Status:** Done

### Subtask 4.3 - Verify and test association displays

**Description**
Test both components with various data scenarios to ensure all association displays work correctly and handle edge cases (no associations, single associations, multiple projects, etc.).

**Target directories**

- app (frontend testing and verification)

**Status:** Done

# Task 5. WaterPumpCard display data

**Description**:

- Show the LIC associated with the water pump controller (if available) in the WaterPumpCard. The LIC is the device that is mapped to the water pump controller via mesh_device_mapping.
  WaterPumpCard is declared in app/src/pages/main/WaterPumpsPage.tsx.
- Additionally to the identifier, Also show the water pump controller label (if available) in the WaterPumpCard.
- Suggestion: Change the grid in the " Info Grid " to 3 columns instead of 2, to accommodate the new information.

**Files to modify**:

- `app/src/pages/main/WaterPumpsPage.tsx`

**Acceptance Criteria**:

- [x] WaterPumpCard displays the LIC associated with the water pump controller (if available)
- [x] WaterPumpCard displays the water pump controller label (if available)
- [x] All existing functionality preserved
- [x] Code passes type checking

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 5.1 - Integrate device mapping utilities and add controller label display

**Description**
Import and integrate existing device label utilities (`device-label-utils.ts`) to resolve water pump controller labels from metadata and display them in the WaterPumpCard info grid.

**Target directories**

- app/src/pages/main (WaterPumpsPage component)
- app/src/utils (device label utilities)

**Status:** Done

### Subtask 5.2 - Add LIC association display through mesh device mapping

**Description**
Use existing mesh device mapping utilities to find and display the LIC device associated with each water pump controller. Update the info grid layout from current 2/4 columns to 3 columns to accommodate the new LIC information field.

**Target directories**

- app/src/pages/main (WaterPumpsPage component)
- app/src/utils (mesh device mapping utilities)

**Status:** Done
