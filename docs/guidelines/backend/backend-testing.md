# Backend Testing

## Overview

Guidelines for testing backend functionality in the Irriga Mais system, focusing on database operations, business logic validation, and data integrity constraints.

Source: Analysis of `/directus/tests/` folder

## Principles

- **Isolation**: Each test runs in its own transaction that is rolled back
- **Real Database**: Tests use actual PostgreSQL database with proper constraints
- **Business Rules**: Validate domain-specific constraints and mesh network rules
- **Data Integrity**: Ensure referential integrity and constraint validation
- **Comprehensive Coverage**: Test both success and failure scenarios

## Test Structure

### Test Framework

- **Runtime**: Bun test framework (`bun:test`)
- **Database**: Knex.js with PostgreSQL
- **Transaction Management**: Each test wrapped in transaction with automatic rollback

### Required Test Setup

```typescript
import { describe, it, beforeEach, afterEach, expect } from "bun:test";
import { Knex } from "knex";
import { begin, rollbackAndDestroy, createKnex } from "./helpers/db";

let knex: Knex;
let trx: Knex.Transaction;

describe("Test Suite Name", () => {
  beforeEach(async () => {
    knex = createKnex();
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });
});
```

## Test Categories

### 1. Constraint Validation Tests

Test database constraints and business rules enforcement.

**Patterns:**

- Test valid scenarios that should succeed
- Test invalid scenarios that should fail with specific constraints
- Use `expectRejectWithinSavepoint` for expected failures

**Example:**

```typescript
it("should succeed when constraint is satisfied", async () => {
  const result = await validOperation(trx);
  expect(result).toBeDefined();
});

it("should fail when constraint is violated", async () => {
  await expectRejectWithinSavepoint(trx, async () => {
    await invalidOperation(trx);
  });
});
```

### 2. Mesh Network Constraint Tests

Validate mesh network topology and device relationships.

**Focus Areas:**

- Same mesh network requirements for related devices
- LIC (Localized Irrigation Controller) as mesh coordinator
- Device type validation (LIC, RM, WPC-PL10, WPC-PL50, VC)

**Test Scenarios:**

- Reservoir: RM and pump must be in same mesh network
- Project: LIC and irrigation pump must be in same mesh network
- Sector: VC and project LIC must be in same mesh network

### 3. Device Mapping Tests

Test mesh device mapping lifecycle and temporal constraints.

**Key Areas:**

- Mapping creation and updates
- Temporal overlap handling and accommodation
- Current mapping updates via triggers
- Historical mapping queries

**Accommodation Logic Tests:**

- Existing starts before new → truncate existing end
- Existing ends after new → move existing start to new end + 1s
- Existing contained within new → error
- Existing contains new → split existing in two
- New with infinite end overlapping → error

### 4. Database Seeding Tests

Validate complete database seeding functionality.

**Validation Points:**

- All required entities created (users, accounts, properties, devices)
- Proper relationships established
- Device identifiers format validation
- Mesh mappings respect property constraints
- Irrigation plans and steps properly sequenced
- Configuration values within expected ranges

## Helper Utilities

### Database Helpers (`helpers/db.ts`)

- `createKnex()`: Create test database connection
- `begin(knex)`: Start transaction for test
- `rollbackAndDestroy(trx)`: Clean up after test
- `expectRejectWithinSavepoint(trx, fn)`: Test expected failures safely

### Fixture Helpers (`helpers/fixtures.ts`)

- Entity creation functions: `insertUser`, `insertAccount`, `insertProperty`, etc.
- Date utilities: `daysAgo(n)`, `plusSeconds(date, seconds)`
- Device-specific helpers for different models
- Relationship mapping helpers

## Environment Configuration

### Test Database Setup

Required environment variables:

- `TEST_DB_HOST`: Database host
- `TEST_DB_USER`: Database user
- `TEST_DB_DATABASE`: Test database name
- `TEST_DB_PASSWORD`: Database password (optional)
- `TEST_DB_PORT`: Database port (default: 5432)

## Testing Conventions

### File Naming

- Pattern: `{feature}.test.ts`
- Location: `/directus/tests/`
- Helpers: `/directus/tests/helpers/`

### Test Structure

```typescript
describe("Feature Name", () => {
  // Setup helper function if complex
  async function setupTestData() {
    // Create required test data
    return {
      /* test objects */
    };
  }

  it("should describe expected behavior", async () => {
    // Arrange
    const setup = await setupTestData();

    // Act
    const result = await performOperation(trx, setup);

    // Assert
    expect(result).toBeDefined();
  });
});
```

### Assertion Patterns

- **Success Cases**: Use standard `expect()` assertions
- **Failure Cases**: Use `expectRejectWithinSavepoint()` wrapper
- **Temporal Comparisons**: Use `closeMs()` helper for date comparisons
- **Array Validations**: Check length, contains, and element properties

## Best Practices

### Do

- Always test both success and failure scenarios
- Use descriptive test names that explain the business rule
- Create setup functions for complex test data scenarios
- Validate all constraint violations explicitly
- Test temporal edge cases (overlaps, boundaries)
- Verify trigger behavior and side effects
- Test batch operations and their rollback behavior

### Don't

- Skip transaction cleanup (always use `afterEach` rollback)
- Test only happy path scenarios
- Ignore database constraint error messages
- Assume test isolation without proper transaction management
- Mix test data between test cases
- Test implementation details instead of behavior

### Error Testing

When testing expected failures:

1. Wrap failing operation in `expectRejectWithinSavepoint`
2. Verify specific constraint or business rule violation
3. Ensure transaction remains usable after expected failure
4. Test cleanup and state consistency

### Performance Considerations

- Use database indexes appropriately (test their existence)
- Validate query performance for complex operations
- Test bulk operations with realistic data volumes
- Monitor test execution time for regression detection

## Checklists

### New Feature Test Checklist

- [ ] Happy path scenarios covered
- [ ] All constraint violations tested
- [ ] Edge cases and boundary conditions
- [ ] Transaction rollback behavior
- [ ] Trigger side effects validated
- [ ] Related entity impacts tested
- [ ] Error messages are appropriate

### Constraint Test Checklist

- [ ] Valid data succeeds
- [ ] Invalid data fails with expected error
- [ ] Business rule explanations clear
- [ ] Cross-entity relationships validated
- [ ] Temporal constraints tested
- [ ] Multi-property isolation verified

### Integration Test Checklist

- [ ] Complete workflow tested end-to-end
- [ ] Multiple entity interactions
- [ ] State consistency across operations
- [ ] Rollback behavior on failures
- [ ] Performance within acceptable bounds

## References

- **Test Files**: `/directus/tests/`
- **Database Schema**: `/docs/001-ENTITIES.md`
- **Business Rules**: `/docs/003-BUSINESS_RULES_AND_CONSTRAINTS.md`
- **Mesh Networks**: `/docs/007-MESH_NETWORK.md`
