# Testing Strategy

Overview
Source: /app/.github/instructions/coding.instructions.md

Principles

- Meaningful coverage over raw percentage.
- Unit tests for components and utilities.

Conventions

- Use Bun test runner.
- Use React Testing Library for components.
- Mock external dependencies.

Patterns

- Test pure utilities directly.
- Test components via behavior and accessible queries.

Examples

- Render components with RTL, assert visible behavior.

Do/Don’t
Do

- Favor integration-style component tests.
- Mock Directus service and network layers.

Don’t

- Over-test implementation details.

Checklists

- Tests run with Bun.
- Mocks for network/services.

References

- /docs/guidelines/frontend/api-and-services.md
- /docs/guidelines/frontend/react.md

Conflicts & Resolutions

- None.
