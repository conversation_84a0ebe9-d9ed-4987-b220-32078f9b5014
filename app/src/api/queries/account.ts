import { readItem, readItems } from "@directus/sdk";
import type { AppDirectusClient } from "../client";
import { OmitAndMerge } from "@/utils/types";
import type { Point, Polygon } from "geojson";
import type { <PERSON><PERSON><PERSON> } from "../model/common";
import type { DayOfWeek } from "../model/common";
import type { WaterPumpType } from "../model/water-pump";
import type { DeviceModel } from "../model/device";
import type { ValveControllerOutput } from "../model/sector";

// Verification: These types should now resolve correctly
// type TestAUTPropertyAccount = AUTProperty["account"]; // Should be string
// type TestAUTPropertyPoint = AUTProperty["point"]; // Should be Point | null
// type TestAUTWaterPumpProperty = AUTWaterPump["property"]; // Should be string
// type TestAUTWaterPumpType = AUTWaterPump["pump_type"]; // Should be WaterPumpType
// type TestAUTReservoirLocation = AUTReservoir["location"]; // Should be Point | null
// type TestAUTSectorPolygon = AUTSector["polygon"]; // Should be Polygon | null

export type AccountUserWithAccount = Awaited<
  ReturnType<typeof listAccountsWithUsers>
>[number];

export async function listAccountsWithUsers(directus: AppDirectusClient) {
  const response = await directus.request(
    readItems("account_user", {
      fields: [
        "id",
        "role",
        "start_date",
        "end_date",
        {
          account: [
            "id",
            {
              owner: [
                "id",
                "first_name",
                "last_name",
                "email",
                "avatar",
                "status",
              ],
            },
          ],
        },
      ],
    })
  );
  return response;
}

export async function getAccountUserTree(
  directus: AppDirectusClient,
  accountUserId: string
) {
  const response = await directus.request(
    readItem("account_user", accountUserId, {
      fields: [
        "id",
        "role",
        "start_date",
        "end_date",
        "metadata",
        "notes",
        {
          account: [
            "id",
            "metadata",
            "notes",
            {
              owner: [
                "id",
                "first_name",
                "last_name",
                "email",
                "avatar",
                "status",
              ],
              properties: [
                "id",
                "account",
                "name",
                "timezone",
                "point",
                "address_postal_code",
                "address_street_name",
                "address_street_number",
                "address_complement",
                "address_neighborhood",
                "address_city",
                "address_state",
                "address_country",
                "backwash_duration_minutes",
                "backwash_period_minutes",
                "backwash_delay_seconds",
                "rain_gauge_enabled",
                "rain_gauge_resolution_mm",
                "precipitation_volume_limit_mm",
                "precipitation_suspended_duration_hours",
                "notes",
                "metadata",
                {
                  water_pumps: [
                    "id",
                    "property",
                    "water_pump_controller",
                    "label",
                    "identifier",
                    "pump_type",
                    "pump_model",
                    "has_frequency_inverter",
                    "monitor_operation",
                    "flow_rate_lh",
                    "notes",
                    "metadata",
                  ],
                  reservoirs: [
                    "id",
                    "property",
                    "name",
                    "reservoir_monitor",
                    "water_pump",
                    "description",
                    "capacity",
                    "location",
                    "enabled",
                    "notes",
                    "metadata",
                  ],
                  projects: [
                    "id",
                    "property",
                    "name",
                    "irrigation_water_pump",
                    "fertigation_water_pump",
                    "localized_irrigation_controller",
                    "description",
                    "pipe_wash_time_seconds",
                    "backwash_duration_seconds",
                    "backwash_period_seconds",
                    "backwash_pump_type",
                    "start_date",
                    "end_date",
                    "metadata",
                    "notes",
                    {
                      irrigation_plans: [
                        "id",
                        "project",
                        "name",
                        "description",
                        "start_time",
                        "days_of_week",
                        "is_enabled",
                        "fertigation_enabled",
                        "total_irrigation_duration",
                        "start_date",
                        "end_date",
                        "metadata",
                        "notes",
                        {
                          steps: [
                            "id",
                            "irrigation_plan",
                            "sector",
                            "description",
                            "order",
                            "duration_seconds",
                            "fertigation_start_delay_seconds",
                            "fertigation_duration_seconds",
                            "notes",
                            "metadata",
                          ],
                        },
                      ],
                      sectors: [
                        "id",
                        "project",
                        "name",
                        "description",
                        "valve_controller",
                        "valve_controller_output",
                        "area",
                        "polygon",
                        "metadata",
                        "notes",
                      ],
                    },
                  ],
                  devices: [
                    "id",
                    "property",
                    "start_date",
                    "end_date",
                    "metadata",
                    "notes",
                    {
                      device: [
                        "id",
                        "identifier",
                        "model",
                        "metadata",
                        "notes",
                      ],
                      current_mesh_device_mapping: [
                        "id",
                        "start_date",
                        "end_date",
                        "mesh_property_device",
                        "lic_property_device",
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    })
  );
  return response as AccountUserTree;
}

// Define the actual query result structure based on what the query returns
// The query selects relation fields directly (like "account") which return string IDs
// and relation fields with nested selections (like water_pumps: [...]) which return full objects
type AUTAccountQueryResult = {
  id: string;
  metadata: AnyJson | null;
  notes: string | null;
  owner: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar: string | null;
    status: string;
  };
  properties: Array<{
    id: string;
    account: string; // Direct selection - returns string ID
    name: string;
    timezone: string;
    point: Point | null;
    address_postal_code: string | null;
    address_street_name: string | null;
    address_street_number: string | null;
    address_complement: string | null;
    address_neighborhood: string | null;
    address_city: string | null;
    address_state: string | null;
    address_country: string | null;
    backwash_duration_minutes: number | null;
    backwash_period_minutes: number | null;
    backwash_delay_seconds: number | null;
    rain_gauge_enabled: boolean;
    rain_gauge_resolution_mm: number | null;
    precipitation_volume_limit_mm: number | null;
    precipitation_suspended_duration_hours: number | null;
    notes: string | null;
    metadata: AnyJson | null;
    water_pumps: Array<{
      id: string;
      property: string; // Direct selection - returns string ID
      water_pump_controller: string; // Direct selection - returns string ID
      label: string;
      identifier: string;
      pump_type: WaterPumpType;
      pump_model: string;
      has_frequency_inverter: boolean;
      monitor_operation: boolean;
      flow_rate_lh: number | null;
      notes: string | null;
      metadata: AnyJson | null;
    }>;
    reservoirs: Array<{
      id: string;
      property: string; // Direct selection - returns string ID
      name: string;
      reservoir_monitor: string; // Direct selection - returns string ID
      water_pump: string; // Direct selection - returns string ID
      description: string | null;
      capacity: number | null;
      location: Point | null;
      enabled: boolean;
      notes: string | null;
      metadata: AnyJson | null;
    }>;
    projects: Array<{
      id: string;
      property: string; // Direct selection - returns string ID
      name: string;
      irrigation_water_pump: string; // Direct selection - returns string ID
      fertigation_water_pump: string; // Direct selection - returns string ID
      localized_irrigation_controller: string; // Direct selection - returns string ID
      description: string | null;
      pipe_wash_time_seconds: number | null;
      backwash_duration_seconds: number | null;
      backwash_period_seconds: number | null;
      backwash_pump_type: "IRRIGATION" | "FERTIGATION" | null;
      start_date: string | null;
      end_date: string | null;
      metadata: AnyJson | null;
      notes: string | null;
      irrigation_plans: Array<{
        id: string;
        project: string; // Direct selection - returns string ID
        name: string;
        description: string | null;
        start_time: string;
        days_of_week: DayOfWeek[];
        is_enabled: boolean;
        fertigation_enabled: boolean;
        backwash_enabled: boolean;
        total_irrigation_duration: number;
        start_date: string | null;
        end_date: string | null;
        metadata: AnyJson | null;
        notes: string | null;
        steps: Array<{
          id: string;
          irrigation_plan: string; // Direct selection - returns string ID
          sector: string; // Direct selection - returns string ID
          description: string | null;
          order: number;
          duration_seconds: number;
          fertigation_start_delay_seconds: number | null;
          fertigation_duration_seconds: number | null;
          notes: string | null;
          metadata: AnyJson | null;
        }>;
      }>;
      sectors: Array<{
        id: string;
        project: string; // Direct selection - returns string ID
        name: string;
        description: string | null;
        valve_controller: string; // Direct selection - returns string ID
        valve_controller_output: ValveControllerOutput;
        area: number | null;
        polygon: Polygon | null;
        metadata: AnyJson | null;
        notes: string | null;
      }>;
    }>;
    devices: Array<{
      id: string;
      property: string; // Direct selection - returns string ID
      start_date: string;
      end_date: string | null;
      metadata: AnyJson | null;
      notes: string | null;
      device: {
        id: string;
        identifier: string;
        model: DeviceModel;
        metadata: AnyJson | null;
        notes: string | null;
      };
      current_mesh_device_mapping: {
        id: string;
        start_date: string;
        end_date: string | null;
        mesh_property_device: string; // Direct selection - returns string ID
        lic_property_device: string; // Direct selection - returns string ID
      };
    }>;
  }>;
};

export type AUTAccountRaw = AUTAccountQueryResult;
export type AUTPropertyRaw = NonNullable<AUTAccountRaw["properties"]>[number];
export type AUTPropertyDeviceRaw = NonNullable<
  AUTPropertyRaw["devices"]
>[number];
export type AUTDeviceRaw = NonNullable<AUTPropertyDeviceRaw["device"]>;
export type AUTCurrentMeshDeviceMappingRaw = NonNullable<
  AUTPropertyDeviceRaw["current_mesh_device_mapping"]
>;
export type AUTWaterPumpRaw = NonNullable<
  AUTPropertyRaw["water_pumps"]
>[number];
export type AUTReservoirRaw = NonNullable<AUTPropertyRaw["reservoirs"]>[number];
export type AUTProjectRaw = NonNullable<AUTPropertyRaw["projects"]>[number];
export type AUTSectorRaw = NonNullable<AUTProjectRaw["sectors"]>[number];
export type AUTIrrigationPlanRaw = NonNullable<
  AUTProjectRaw["irrigation_plans"]
>[number];
export type AUTPropertyIrrigationPlanStepRaw = NonNullable<
  AUTIrrigationPlanRaw["steps"]
>[number];

// Verification: All types now resolve correctly with proper TypeScript types
// Relation fields: AUTProperty["account"] -> string, AUTWaterPump["property"] -> string
// Non-relation fields: AUTProperty["point"] -> Point | null, AUTReservoir["location"] -> Point | null

// Define leaf types first (no child arrays to override)
export type AUTDevice = AUTDeviceRaw;

export type AUTCurrentMeshDeviceMapping = OmitAndMerge<
  AUTCurrentMeshDeviceMappingRaw,
  "mesh_property_device" | "lic_property_device",
  {
    mesh_property_device: string;
    lic_property_device: string;
  }
>;

export type AUTPropertyIrrigationPlanStep = OmitAndMerge<
  AUTPropertyIrrigationPlanStepRaw,
  "irrigation_plan" | "sector",
  {
    irrigation_plan: string;
    sector: string;
  }
>;

export type AUTWaterPump = OmitAndMerge<
  AUTWaterPumpRaw,
  "property" | "water_pump_controller",
  {
    property: string;
    water_pump_controller: string | null;
  }
>;

export type AUTReservoir = OmitAndMerge<
  AUTReservoirRaw,
  "property" | "reservoir_monitor" | "water_pump",
  {
    property: string;
    reservoir_monitor: string;
    water_pump: string;
  }
>;
export type AUTSector = OmitAndMerge<
  AUTSectorRaw,
  "project" | "valve_controller",
  {
    project: string;
    valve_controller: string;
  }
>;

// Define types with child arrays that need to reference refined child types
export type AUTIrrigationPlan = OmitAndMerge<
  AUTIrrigationPlanRaw,
  "project" | "steps",
  {
    project: string;
    steps: AUTPropertyIrrigationPlanStep[];
  }
>;

export type AUTProject = OmitAndMerge<
  AUTProjectRaw,
  | "property"
  | "irrigation_water_pump"
  | "fertigation_water_pump"
  | "localized_irrigation_controller"
  | "irrigation_plans"
  | "sectors",
  {
    property: string;
    irrigation_water_pump: string;
    fertigation_water_pump: string;
    localized_irrigation_controller: string;
    irrigation_plans: AUTIrrigationPlan[];
    sectors: AUTSector[];
  }
>;

export type AUTPropertyDevice = OmitAndMerge<
  AUTPropertyDeviceRaw,
  "property" | "device" | "current_mesh_device_mapping",
  {
    property: string;
    device: AUTDevice;
    current_mesh_device_mapping: AUTCurrentMeshDeviceMapping;
  }
>;

export type AUTProperty = OmitAndMerge<
  AUTPropertyRaw,
  "account" | "water_pumps" | "reservoirs" | "projects" | "devices",
  {
    account: string;
    water_pumps: AUTWaterPump[];
    reservoirs: AUTReservoir[];
    projects: AUTProject[];
    devices: AUTPropertyDevice[];
  }
>;

export type AUTAccount = OmitAndMerge<
  AUTAccountRaw,
  "properties",
  {
    properties: AUTProperty[];
  }
>;

export type AccountUserTree = {
  id: string;
  role: string;
  start_date: string;
  end_date: string | null;
  metadata: AnyJson | null;
  notes: string | null;
  account: AUTAccount;
};
