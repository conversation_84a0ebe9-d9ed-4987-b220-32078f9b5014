import { WATER_PUMP_TYPE_LABELS } from '@/api/model/water-pump';
import type { AUTWaterPump, AUTPropertyDevice, AUTProperty } from '@/api/queries/account';
import { resolveDeviceDisplayInfo, formatDeviceDisplayText, formatLICDisplayText } from '@/utils/device-label-utils';
import { findWaterPumpAssociations, formatDeviceAssociations } from '@/utils/device-associations';
import { ChevronRight, Wrench } from 'lucide-react';

interface PumpListItemProps {
  pump: AUTWaterPump;
  propertyDevices: AUTPropertyDevice[];
  selectedProperty: AUTProperty | null;
  onClick: (pump: AUTWaterPump) => void;
}

function getPumpLastActivity(_pump: AUTWaterPump) {
  // This would come from the API with real activity data
  return 'Agora';
}

export default function PumpListItem({ pump, propertyDevices, selectedProperty, onClick }: PumpListItemProps) {
  // Resolve pump controller display information
  const pumpControllerInfo = resolveDeviceDisplayInfo(
    pump.water_pump_controller,
    propertyDevices
  );

  // Get water pump associations
  const associations = findWaterPumpAssociations(pump.id, selectedProperty);
  const associationText = formatDeviceAssociations(associations);

  return (
    <div
      onClick={() => onClick(pump)}
      className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <Wrench className="text-gray-400" size={16} />
            <span className="font-medium text-gray-900">
              {pump.label}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-1">
            S/N: {pump.identifier}
          </p>
          <p className="text-xs text-gray-500 mb-1">
            Tipo:{' '}
            {WATER_PUMP_TYPE_LABELS[pump.pump_type] || pump.pump_type}
          </p>
          <p className="text-xs text-gray-500 mb-1">
            Controlador: {formatDeviceDisplayText(pumpControllerInfo, 'Não definido')}
          </p>
          {pumpControllerInfo?.licInfo && (
            <p className="text-xs text-gray-500 mb-1">
              LIC: {formatLICDisplayText(pumpControllerInfo.licInfo)}
            </p>
          )}
          {associationText && (
            <div className="flex items-center gap-1 text-xs text-blue-600 mb-1">
              <span>🔗</span>
              <span>{associationText}</span>
            </div>
          )}
          <p className="text-xs text-gray-500 mb-1">
            Inversor de frequência:{' '}
            {pump.has_frequency_inverter ? 'Sim' : 'Não'}
          </p>
          <p className="text-xs text-gray-500 mb-1">
            Monitoramento:{' '}
            {pump.monitor_operation ? 'Ativo' : 'Inativo'}
          </p>
          <p className="text-xs text-gray-400">
            Última atividade: {getPumpLastActivity(pump)}
          </p>
        </div>
        <ChevronRight className="text-gray-400" size={20} />
      </div>
    </div>
  );
}