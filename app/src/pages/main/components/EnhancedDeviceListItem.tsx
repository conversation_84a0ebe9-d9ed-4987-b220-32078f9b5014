import { Link, AlertTriangle } from "lucide-react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import type { AUTProperty } from "@/api/queries/account";
import { getDeviceIcon, getTreeSymbol } from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";
import { findDeviceAssociations, formatDeviceAssociations, getAssociationIcon, findValveControllerSectorProjectAssociations, formatValveControllerAssociations } from "@/utils/device-associations";

interface EnhancedDeviceListItemProps {
  device: DeviceWithMapping;
  property: AUTProperty | null;
  index: number;
  devices: DeviceWithMapping[];
  onDeviceClick: (device: DeviceWithMapping) => void;
  onManageNetwork?: (device: DeviceWithMapping) => void;
}

function EnhancedDeviceListItem({
  device,
  property,
  index,
  devices,
  onDeviceClick,
  onManageNetwork,
}: EnhancedDeviceListItemProps) {
  const treeSymbol = getTreeSymbol(device, index, devices);
  const deviceIcon = getDeviceIcon(device.device.model);
  const modelLabel = getDeviceModelLabel(device.device.model);
  
  // Get device associations
  let associationText: string | null = null;
  
  if (device.device.model === 'VC') {
    // For valve controllers, use the enhanced sector-project grouping
    const vcAssociations = findValveControllerSectorProjectAssociations(device.device.id, property);
    associationText = formatValveControllerAssociations(vcAssociations);
  } else {
    // For other devices, use the standard association formatting
    const associations = findDeviceAssociations(device.device.id, device.device.model, property);
    associationText = formatDeviceAssociations(associations);
  }

  // Get background and border colors based on device status
  const getDeviceStyles = () => {
    switch (device.mappingStatus) {
      case "coordinator":
        return {
          background: "bg-green-50",
          border: "border-green-200",
          hover: "hover:bg-green-100",
        };
      case "mapped":
        return {
          background: "bg-gray-50",
          border: "border-gray-200",
          hover: "hover:bg-gray-100",
        };
      case "unmapped":
        return {
          background: "bg-yellow-50",
          border: "border-yellow-200",
          hover: "hover:bg-yellow-100",
        };
      default:
        return {
          background: "bg-white",
          border: "border-gray-200",
          hover: "hover:bg-gray-50",
        };
    }
  };

  const styles = getDeviceStyles();

  const handleClick = () => {
    if (device.mappingStatus === "coordinator" && onManageNetwork) {
      onManageNetwork(device);
    } else {
      onDeviceClick(device);
    }
  };

  const getLastActivity = () => {
    // This would come from real API data
    return "2 min atrás";
  };

  const renderMappingInfo = () => {
    switch (device.mappingStatus) {
      case "coordinator":
        const meshCount = device.meshDevices?.length || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-green-600">
            <Link size={14} />
            <span>
              Coordenador para {meshCount} dispositivo
              {meshCount !== 1 ? "s" : ""}
            </span>
          </div>
        );

      case "mapped":
        if (device.licDevice && device.current_mesh_device_mapping) {
          return (
            <div className="flex items-center gap-1 text-sm text-blue-600">
              <span>📡</span>
              <span>Mapeado para {device.licDevice.device.identifier}</span>
            </div>
          );
        }
        break;

      case "unmapped":
        return (
          <div className="flex items-center gap-1 text-sm text-red-600">
            <AlertTriangle size={14} />
            <span>Não mapeado</span>
          </div>
        );
    }
    return null;
  };

  // Get label from property_device metadata
  const label =
    device.metadata &&
    typeof device.metadata === "object" &&
    "label" in device.metadata
      ? device.metadata.label
      : null;

  return (
    <div
      onClick={handleClick}
      className={`${styles.background} border ${styles.border} rounded-lg p-4 cursor-pointer ${styles.hover} transition-colors`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {/* Device Header */}
          <div className="flex items-center gap-2 mb-1">
            {/* Tree Symbol for Hierarchy */}
            {treeSymbol && (
              <span className="text-gray-400 font-mono text-sm mr-1">
                {treeSymbol}
              </span>
            )}

            {/* Device Icon and Status */}
            <span className="text-lg">{deviceIcon}</span>
            {device.mappingStatus === "unmapped" && (
              <AlertTriangle className="text-orange-500" size={16} />
            )}

            {/* Device Label or Serial Number */}
            {label && label.trim() ? (
              <span
                className="font-semibold text-gray-900 text-base truncate max-w-xs"
                title={label}
              >
                {label}
              </span>
            ) : (
              <span className="font-medium text-gray-900">
                S/N: {device.device.identifier}
              </span>
            )}
          </div>

          {/* If label exists, show serial number as subtext for clarity */}
          {label && label.trim() && (
            <p className="text-xs text-gray-500 ml-6">
              S/N: {device.device.identifier}
            </p>
          )}

          {/* Device Model */}
          <p className="text-sm text-gray-600 mb-1 ml-6">{modelLabel}</p>

          {/* Mapping Information */}
          <div className="ml-6 mb-1">{renderMappingInfo()}</div>

          {/* Device Associations */}
          {associationText && (
            <div className="flex items-center gap-1 text-sm text-blue-600 ml-6 mb-1">
              <span>🔗</span>
              <span>{associationText}</span>
            </div>
          )}

          {/* Last Activity */}
          <p className="text-xs text-gray-400 ml-6">
            Última atividade: {getLastActivity()}
          </p>
        </div>
      </div>
    </div>
  );
}

export default EnhancedDeviceListItem;
