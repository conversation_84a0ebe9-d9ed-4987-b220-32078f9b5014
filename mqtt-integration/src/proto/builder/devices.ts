import { codec } from "proto";
import type { SQL } from "bun";
import {
  listFullProjectsByLICIdentifier,
  listProjectsByLICIdentifier,
} from "../../db/queries/project-queries";
import type {
  ReservoirWithMonitorAndWaterPumpWithController,
  SectorWithValveController,
  WaterPumpWithController,
} from "../../db/queries/types";
import { listServiceWaterPumpsByLICIdentifier } from "../../db/queries/water-pump";
import { listReservoirsWithMonitorAndWaterPumpByLICIdentifier } from "../../db/queries/reservoir";

const PULSE = 0x01;
const MONI = 0x02;

function getDeviceParamsForIrrigationPumpPL10(
  irrigationPump: WaterPumpWithController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  const isPulse = false; // TODO: irrigationPump.mode === "PULSE";

  const pumpLink = irrigationPump.controller;
  const meshId = Number.parseInt(pumpLink.identifier, 16);
  const deviceIdentity = 0; // Specific logic for IrrigationPump
  const deviceType = 1; // IrrigationPump
  const out1 = 1; // "out1" to "1",
  const out2 = 0; // "out2" to "0",
  const input = irrigationPump.monitor_operation ? 1 : 0; // "input" to "0",
  const mode = (isPulse ? PULSE : 0) | (input ? MONI : 0);
  const sector = null; // "sector" to null

  return {
    meshId,
    deviceId: deviceIdentity,
    deviceType,
    out1,
    out2,
    input,
    mode,
    sector,
    groupIdx,
    idx: ord_idx,
  };
}

function getDeviceParamsForIrrigationPumpPL50(
  irrigationPump: WaterPumpWithController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  const isPulse = false; // TODO: irrigationPump.mode === "PULSE";

  const pumpLink = irrigationPump.controller;
  const meshId = Number.parseInt(pumpLink.identifier, 16);
  const deviceIdentity = 0; // Specific logic for IrrigationPump
  const deviceType = 1; // IrrigationPump
  const out1 = 1; // "out1" to "1",
  const out2 = isPulse ? 2 : 0; // "out2" to "2" if isPulse, else "0"
  const input = irrigationPump.monitor_operation ? 1 : 0; // "input" to "0",
  const mode = (isPulse ? PULSE : 0) | (input ? MONI : 0);
  const sector = null; // "sector" to null

  return {
    meshId,
    deviceId: deviceIdentity,
    deviceType,
    out1,
    out2,
    input,
    mode,
    sector,
    groupIdx,
    idx: ord_idx,
  };
}

function getDeviceParamsForFertigationPumpPL10(
  irrigationPump: WaterPumpWithController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  /*
    val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "1",
                                "type" to DevType.Ferti.value,
                                "out1" to "2",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
    */

  const pumpLink = irrigationPump.controller;
  const meshId = Number.parseInt(pumpLink.identifier, 16);
  const deviceIdentity = 1; // Specific logic for FertigationPump
  const deviceType = 2; // Ferti
  const out1 = 2; // "out1" to "2", Specifically for FertigationPump PL10
  const out2 = 0; // "out2" to "0", Specifically for FertigationPump PL10
  const input = 0; // "input" to "0",
  const mode = 0; // "mode" to "0",
  // Note: Fertigation pumps do not have a monitor operation input
  // so we set "input" to 0 and "mode" to 0.
  const sector = null; // "sector" to null

  return {
    meshId,
    deviceId: deviceIdentity,
    deviceType,
    out1,
    out2,
    input,
    mode,
    sector,
    groupIdx,
    idx: ord_idx,
  };
}

function getDeviceParamsForServicePumpPL10(
  servicePump: WaterPumpWithController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  /*
    
    val input = if (checkInput) "1" else "0"
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.ServicePump.value,
                                "out1" to "1",
                                "out2" to "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
    */
  const isPulse = false; // TODO: irrigationPump.mode === "PULSE";
  const mode =
    (isPulse ? PULSE : 0) | (servicePump.monitor_operation ? MONI : 0);
  return {
    meshId: Number.parseInt(servicePump.controller.identifier, 16),
    deviceId: 0, // Specific logic for ServicePump
    deviceType: 3, // ServicePump
    out1: 1, // "out1" to "1",
    out2: 0, // "out2" to "0",
    input: servicePump.monitor_operation ? 1 : 0, // "input" to "0",
    mode,
    sector: null, // "sector" to null
    groupIdx,
    idx: ord_idx,
  };
}

function getDeviceParamsForReservoirMonitor(
  reservoirMonitor: ReservoirWithMonitorAndWaterPumpWithController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  /*
  val device = mapOf(
                        "mesh_idx" to meshIdx,
                        "identity" to "0",
                        "type" to DevType.Level.value,
                        "out1" to "0",
                        "out2" to "0",
                        "input" to "0",
                        "mode" to 0,
                        "sector" to null
                    )
                    devicesToInsert.add(device)
                    */
  throw new Error("Reservoir monitor not implemented yet");
}

function getDeviceParamsForSector(
  sector: SectorWithValveController,
  ord_idx: number,
  groupIdx: number
): codec.in_.devices.IDevicesData {
  throw new Error("Sector not implemented yet");
}

// Verify if PL50 can be used for fertigation

// function getDeviceParamsForFertigationPumpPL50(
//   irrigationPump: WaterPumpWithController,
//   ord_idx: number,
//   groupIdx: number
// ): codec.in_.devices.IDevicesData {
//   const isPulse = false; // TODO: irrigationPump.mode === "PULSE";

//   const pumpLink = irrigationPump.controller;
//   const meshId = Number.parseInt(pumpLink.identifier, 16);
//   const deviceIdentity = 0; // Specific logic for IrrigationPump
//   const deviceType = 2; // Ferti
//   const out1 = 1; // "out1" to "1",
//   const out2 = 0; // "out2" to "0",
//   const input = irrigationPump.monitor_operation ? 1 : 0; // "input" to "0",
//   const mode = (isPulse ? PULSE : 0) | (input ? MONI : 0);
//   const sector = null; // "sector" to null

//   return {
//     meshId,
//     deviceId: deviceIdentity,
//     deviceType,
//     out1,
//     out2,
//     input,
//     mode,
//     sector,
//     groupIdx,
//     idx: ord_idx,
//   };
// }

export async function buildConfig(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<codec.in_.devices.DevicesPackage> {
  // Projects can be mapped to "group" in the DevicesPackage
  const data: codec.in_.devices.IDevicesData[] = [];
  const projects = await listFullProjectsByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );
  let ord_idx = 0;
  for (let i = 0; i < projects.length; i++) {
    const project = projects[i]!;
    const groupIdx = i;

    // Irrigation water pump
    const irrigationPump = project.irrigation_water_pump;
    const pumpLink = irrigationPump.controller;
    if (pumpLink.model === "WPC-PL10") {
      // Handle WPC-PL10 specific logic
      data.push(
        getDeviceParamsForIrrigationPumpPL10(
          irrigationPump,
          ord_idx++,
          groupIdx
        )
      );
    } else if (pumpLink.model === "WPC-PL50") {
      // Handle WPC-PL50 specific logic
      data.push(
        getDeviceParamsForIrrigationPumpPL50(
          irrigationPump,
          ord_idx++,
          groupIdx
        )
      );
    }

    // Fertigation water pump
    if (project.fertigation_water_pump) {
      const fertigationPump = project.fertigation_water_pump;
      const fertigationPumpLink = fertigationPump.controller;
      if (fertigationPumpLink.model === "WPC-PL10") {
        // Handle WPC-PL10 specific logic
        data.push(
          getDeviceParamsForFertigationPumpPL10(
            fertigationPump,
            ord_idx++,
            groupIdx
          )
        );
      } else if (fertigationPumpLink.model === "WPC-PL50") {
        // Handle WPC-PL50 specific logic
        // TODO: Verify if PL50 can be used for fertigation
        // data.push(
        //   getDeviceParamsForFertigationPumpPL50(
        //     fertigationPump,
        //     ord_idx++,
        //     groupIdx
        //   )
        // );
      }
    }

    // Handle backwash pump based on project configuration
    if (project.backwash_pump_type === "IRRIGATION") {
      // Use the irrigation pump for backwashing
      const pumpLink = irrigationPump.controller;
      if (pumpLink.model === "WPC-PL10") {
        data.push(
          getDeviceParamsForIrrigationPumpPL10(
            irrigationPump,
            ord_idx++,
            groupIdx
          )
        );
      } else if (pumpLink.model === "WPC-PL50") {
        data.push(
          getDeviceParamsForIrrigationPumpPL50(
            irrigationPump,
            ord_idx++,
            groupIdx
          )
        );
      }
    } else if (project.backwash_pump_type === "FERTIGATION" && project.fertigation_water_pump) {
      // Use the fertigation pump for backwashing
      const fertigationPump = project.fertigation_water_pump;
      const fertigationPumpLink = fertigationPump.controller;
      if (fertigationPumpLink.model === "WPC-PL10") {
        data.push(
          getDeviceParamsForFertigationPumpPL10(
            fertigationPump,
            ord_idx++,
            groupIdx
          )
        );
      }
      // Note: PL50 fertigation backwash is not supported yet
    }
    // If backwash_pump_type is null, no backwashing is configured

    // Handle service pump and reservoir only for the first projects use the same LIC
    if (i === 0) {
      // Service water pumps
      const servicePumps = await listServiceWaterPumpsByLICIdentifier(
        db,
        licIdentifier,
        referenceDate
      );
      servicePumps.forEach((servicePump) => {
        data.push(
          getDeviceParamsForServicePumpPL10(servicePump, ord_idx++, groupIdx)
        );
      });

      // Reservoirs with monitor and water pump
      const reservoirs =
        await listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
          db,
          licIdentifier,
          referenceDate
        );
      reservoirs.forEach((reservoir) => {
        data.push(
          getDeviceParamsForReservoirMonitor(reservoir, ord_idx++, groupIdx)
        );
      });
    }

    // Valve controllers (VC)
    if (project.sectors.length > 0) {
      for (const sector of project.sectors) {
        const vcDevice = sector.valve_controller_device;
        if (vcDevice) {
          // Add Valve Controller device
          // TODO: create a specific function for Valve Controller
          // This is a placeholder for the specific logic for Valve Controller
          data.push(getDeviceParamsForSector(sector, ord_idx++, groupIdx));
        }
      }
    }
  }
  const devicesPackage = codec.in_.devices.DevicesPackage.create({
    data,
  });

  // Return the constructed DevicesPackage
  return devicesPackage;
}
